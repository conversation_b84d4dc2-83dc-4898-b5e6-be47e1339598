#!/usr/bin/env node

import chalk from 'chalk';
import fs from 'fs-extra';
import path from 'path';
import config from './src/config/config.js';

// Import all modules
import YouTubeDownloader from './src/youtube/downloader.js';
import YouTubeUploader from './src/youtube/uploader.js';
import InstagramDownloader from './src/instagram/downloader.js';
import InstagramUploader from './src/instagram/uploader.js';
import VideoProcessor from './src/video/processor.js';

class AutomationManager {
  constructor() {
    this.youtubeDownloader = new YouTubeDownloader();
    this.youtubeUploader = new YouTubeUploader();
    this.instagramDownloader = new InstagramDownloader();
    this.instagramUploader = new InstagramUploader();
    this.videoProcessor = new VideoProcessor();
  }

  async initialize() {
    try {
      console.log(chalk.blue('🚀 Initializing Automation System...'));
      
      // Create necessary directories
      await fs.ensureDir('./downloads/youtube');
      await fs.ensureDir('./downloads/instagram');
      await fs.ensureDir('./output');
      await fs.ensureDir('./temp');
      await fs.ensureDir('./screenshots');
      
      // Initialize all components
      await this.youtubeDownloader.initialize();
      await this.videoProcessor.initialize();
      
      console.log(chalk.green('✅ Automation system initialized successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize automation system:'), error);
      throw error;
    }
  }

  async runYouTubeToShortsWorkflow() {
    try {
      console.log(chalk.blue('🎬 Starting YouTube to Shorts workflow...'));
      
      // Step 1: Download videos from trusted channels
      console.log(chalk.yellow('📥 Downloading videos from trusted channels...'));
      const downloadResults = await this.youtubeDownloader.downloadFromTrustedChannels(2);
      
      const successfulDownloads = downloadResults.filter(result => result.success);
      
      if (successfulDownloads.length === 0) {
        console.log(chalk.yellow('⚠️ No videos downloaded successfully'));
        return;
      }
      
      console.log(chalk.green(`✅ Downloaded ${successfulDownloads.length} videos`));
      
      // Step 2: Process videos into multiple shorts
      const processedVideos = [];
      for (const download of successfulDownloads) {
        try {
          console.log(chalk.yellow(`🎞️ Processing: ${download.video}`));

          const multipleShorts = await this.videoProcessor.createMultipleShorts(
            download.filePath,
            'youtube',
            null  // Create as many 1-minute clips as possible (max 10)
          );

          // Add metadata to each short
          for (const processed of multipleShorts) {
            processedVideos.push({
              ...processed,
              originalTitle: download.video,
              channel: download.channel
            });
          }
        } catch (error) {
          console.error(chalk.red(`❌ Failed to process ${download.video}:`), error);
        }
      }
      
      if (processedVideos.length === 0) {
        console.log(chalk.yellow('⚠️ No videos processed successfully'));
        return;
      }

      console.log(chalk.green(`✅ Processed ${processedVideos.length} video clips`));

      // Step 2.5: Remove duplicate files
      await this.videoProcessor.removeDuplicateFiles();

      // Step 3: Upload to YouTube
      console.log(chalk.yellow('📤 Uploading shorts to YouTube...'));
      await this.youtubeUploader.initialize();
      
      const uploadResults = [];
      for (const video of processedVideos) {
        try {
          const metadata = {
            title: `${video.originalTitle} - Shorts`,
            description: `Automated short from ${video.channel}\n\n#Shorts #AutomatedContent`,
            tags: ['Shorts', 'AutomatedContent', 'YouTube'],
            visibility: 'public'
          };
          
          const result = await this.youtubeUploader.uploadShorts(video.outputPath, metadata);
          uploadResults.push(result);
        } catch (error) {
          console.error(chalk.red(`❌ Failed to upload ${video.originalTitle}:`), error);
        }
      }
      
      await this.youtubeUploader.close();
      
      // Summary
      const successfulUploads = uploadResults.filter(result => result.success);
      console.log(chalk.green(`✅ YouTube workflow completed: ${successfulUploads.length}/${processedVideos.length} videos uploaded`));
      
      return {
        downloaded: successfulDownloads.length,
        processed: processedVideos.length,
        uploaded: successfulUploads.length
      };
    } catch (error) {
      console.error(chalk.red('❌ YouTube workflow failed:'), error);
      throw error;
    }
  }

  async runInstagramReelsWorkflow() {
    try {
      console.log(chalk.blue('📱 Starting Instagram Reels workflow...'));
      
      // Step 1: Download reels from Instagram
      await this.instagramDownloader.initialize();
      
      console.log(chalk.yellow('📥 Downloading reels from Instagram...'));
      const downloadResults = await this.instagramDownloader.downloadReelsFromHashtag('viral', 3);
      
      await this.instagramDownloader.close();
      
      const successfulDownloads = downloadResults.filter(result => result.success);
      
      if (successfulDownloads.length === 0) {
        console.log(chalk.yellow('⚠️ No reels downloaded successfully'));
        return;
      }
      
      console.log(chalk.green(`✅ Downloaded ${successfulDownloads.length} reels`));
      
      // Step 2: Process reels for Instagram format
      const processedReels = [];
      for (const download of successfulDownloads) {
        try {
          console.log(chalk.yellow(`🎞️ Processing reel: ${path.basename(download.filePath)}`));
          
          const processed = await this.videoProcessor.processVideoForPlatform(
            download.filePath,
            'instagram',
            {
              caption: download.metadata?.caption || 'Automated repost'
            }
          );
          
          processedReels.push({
            ...processed,
            originalMetadata: download.metadata
          });
        } catch (error) {
          console.error(chalk.red(`❌ Failed to process reel:`), error);
        }
      }
      
      if (processedReels.length === 0) {
        console.log(chalk.yellow('⚠️ No reels processed successfully'));
        return;
      }
      
      // Step 3: Upload to Instagram
      console.log(chalk.yellow('📤 Uploading reels to Instagram...'));
      await this.instagramUploader.initialize();
      
      const uploadResults = [];
      for (const reel of processedReels) {
        try {
          const metadata = {
            caption: `${reel.originalMetadata?.caption || 'Check this out!'}\n\n#reels #viral #automated`,
            tags: ['reels', 'viral', 'content']
          };
          
          const result = await this.instagramUploader.uploadReel(reel.outputPath, metadata);
          uploadResults.push(result);
        } catch (error) {
          console.error(chalk.red(`❌ Failed to upload reel:`), error);
        }
      }
      
      await this.instagramUploader.close();
      
      // Summary
      const successfulUploads = uploadResults.filter(result => result.success);
      console.log(chalk.green(`✅ Instagram workflow completed: ${successfulUploads.length}/${processedReels.length} reels uploaded`));
      
      return {
        downloaded: successfulDownloads.length,
        processed: processedReels.length,
        uploaded: successfulUploads.length
      };
    } catch (error) {
      console.error(chalk.red('❌ Instagram workflow failed:'), error);
      throw error;
    }
  }

  async runFullWorkflow() {
    try {
      console.log(chalk.blue('🔄 Starting full automation workflow...'));
      
      const results = {
        youtube: null,
        instagram: null,
        startTime: new Date(),
        endTime: null
      };
      
      // Run YouTube workflow
      try {
        results.youtube = await this.runYouTubeToShortsWorkflow();
      } catch (error) {
        console.error(chalk.red('❌ YouTube workflow failed:'), error);
        results.youtube = { error: error.message };
      }
      
      // Wait between workflows
      console.log(chalk.yellow('⏳ Waiting 30 seconds between workflows...'));
      await new Promise(resolve => setTimeout(resolve, 30000));
      
      // Run Instagram workflow
      try {
        results.instagram = await this.runInstagramReelsWorkflow();
      } catch (error) {
        console.error(chalk.red('❌ Instagram workflow failed:'), error);
        results.instagram = { error: error.message };
      }
      
      results.endTime = new Date();
      
      // Cleanup
      await this.cleanup();
      
      // Print summary
      this.printSummary(results);
      
      return results;
    } catch (error) {
      console.error(chalk.red('❌ Full workflow failed:'), error);
      throw error;
    }
  }

  async cleanup() {
    try {
      console.log(chalk.yellow('🧹 Cleaning up...'));
      
      await this.youtubeDownloader.cleanup();
      await this.instagramDownloader.cleanup();
      await this.videoProcessor.cleanup();
      
      console.log(chalk.green('✅ Cleanup completed'));
    } catch (error) {
      console.error(chalk.red('❌ Cleanup failed:'), error);
    }
  }

  printSummary(results) {
    console.log(chalk.blue('\n📊 WORKFLOW SUMMARY'));
    console.log(chalk.blue('=================='));
    
    const duration = (results.endTime - results.startTime) / 1000 / 60;
    console.log(chalk.white(`Duration: ${duration.toFixed(2)} minutes`));
    
    if (results.youtube) {
      console.log(chalk.yellow('\n📺 YouTube:'));
      if (results.youtube.error) {
        console.log(chalk.red(`   Error: ${results.youtube.error}`));
      } else {
        console.log(chalk.green(`   Downloaded: ${results.youtube.downloaded}`));
        console.log(chalk.green(`   Processed: ${results.youtube.processed}`));
        console.log(chalk.green(`   Uploaded: ${results.youtube.uploaded}`));
      }
    }
    
    if (results.instagram) {
      console.log(chalk.yellow('\n📱 Instagram:'));
      if (results.instagram.error) {
        console.log(chalk.red(`   Error: ${results.instagram.error}`));
      } else {
        console.log(chalk.green(`   Downloaded: ${results.instagram.downloaded}`));
        console.log(chalk.green(`   Processed: ${results.instagram.processed}`));
        console.log(chalk.green(`   Uploaded: ${results.instagram.uploaded}`));
      }
    }
    
    console.log(chalk.blue('\n==================\n'));
  }
}

// Main execution
async function main() {
  try {
    const automation = new AutomationManager();
    await automation.initialize();
    
    const args = process.argv.slice(2);
    const command = args[0] || 'full';
    
    switch (command) {
      case 'youtube':
        await automation.runYouTubeToShortsWorkflow();
        break;
      case 'instagram':
        await automation.runInstagramReelsWorkflow();
        break;
      case 'full':
      default:
        await automation.runFullWorkflow();
        break;
    }
    
    console.log(chalk.green('🎉 Automation completed successfully!'));
  } catch (error) {
    console.error(chalk.red('💥 Automation failed:'), error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️ Received SIGINT, shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n⚠️ Received SIGTERM, shutting down gracefully...'));
  process.exit(0);
});

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default AutomationManager;
