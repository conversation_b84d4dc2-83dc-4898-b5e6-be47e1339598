import ffmpegPkg from 'fluent-ffmpeg';
const ffmpeg = ffmpegPkg;
import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import config from '../config/config.js';

export class VideoProcessor {
  constructor() {
    this.outputDir = config.paths.output;
    this.tempDir = config.paths.temp;
  }

  async initialize() {
    try {
      await fs.ensureDir(this.outputDir);
      await fs.ensureDir(this.tempDir);
      console.log(chalk.green('✅ Video processor initialized'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize video processor:'), error);
      throw error;
    }
  }

  async convertToShorts(inputPath, options = {}) {
    try {
      console.log(chalk.blue(`🎬 Converting to shorts format: ${path.basename(inputPath)}`));
      
      const outputPath = this.generateOutputPath(inputPath, 'shorts');
      const format = options.platform === 'instagram' ? 'instagram' : 'youtube';
      const videoConfig = config.video.formats[format];
      
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .size(`${videoConfig.width}x${videoConfig.height}`)
          .fps(videoConfig.fps)
          .videoCodec('libopenh264')
          .audioCodec('aac')
          .audioBitrate('128k')
          .videoBitrate('2000k')
          .autopad(true, 'black')
          .duration(config.video.maxDuration)
          .output(outputPath)
          .on('start', () => {
            console.log(chalk.blue('🔄 FFmpeg process started'));
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              console.log(chalk.yellow(`⏳ Progress: ${Math.round(progress.percent)}%`));
            }
          })
          .on('end', () => {
            console.log(chalk.green(`✅ Conversion completed: ${outputPath}`));
            resolve({
              success: true,
              outputPath,
              format: format
            });
          })
          .on('error', (error) => {
            console.error(chalk.red('❌ FFmpeg error:'), error);
            reject(error);
          })
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Video conversion failed:'), error);
      throw error;
    }
  }

  async extractHighlights(inputPath, segments = 3) {
    try {
      console.log(chalk.blue(`✂️ Extracting highlights from: ${path.basename(inputPath)}`));
      
      const duration = await this.getVideoDuration(inputPath);
      const segmentDuration = Math.min(config.video.maxDuration / segments, duration / segments);
      const highlights = [];
      
      for (let i = 0; i < segments; i++) {
        const startTime = (duration / segments) * i;
        const outputPath = this.generateOutputPath(inputPath, `highlight-${i + 1}`);
        
        await this.extractSegment(inputPath, outputPath, startTime, segmentDuration);
        highlights.push(outputPath);
      }
      
      console.log(chalk.green(`✅ Extracted ${highlights.length} highlights`));
      return highlights;
    } catch (error) {
      console.error(chalk.red('❌ Highlight extraction failed:'), error);
      throw error;
    }
  }

  async extractSegment(inputPath, outputPath, startTime, duration) {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .seekInput(startTime)
        .duration(duration)
        .output(outputPath)
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .run();
    });
  }

  async addCaptions(inputPath, captionText, options = {}) {
    try {
      console.log(chalk.blue(`📝 Adding captions to: ${path.basename(inputPath)}`));
      
      const outputPath = this.generateOutputPath(inputPath, 'captioned');
      const fontSize = options.fontSize || 24;
      const fontColor = options.fontColor || 'white';
      const backgroundColor = options.backgroundColor || 'black@0.5';
      
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .videoFilters([
            {
              filter: 'drawtext',
              options: {
                text: captionText,
                fontsize: fontSize,
                fontcolor: fontColor,
                box: 1,
                boxcolor: backgroundColor,
                boxborderw: 5,
                x: '(w-text_w)/2',
                y: 'h-th-20'
              }
            }
          ])
          .output(outputPath)
          .on('end', () => {
            console.log(chalk.green(`✅ Captions added: ${outputPath}`));
            resolve(outputPath);
          })
          .on('error', reject)
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Caption addition failed:'), error);
      throw error;
    }
  }

  async addWatermark(inputPath, watermarkPath, options = {}) {
    try {
      console.log(chalk.blue(`🏷️ Adding watermark to: ${path.basename(inputPath)}`));
      
      const outputPath = this.generateOutputPath(inputPath, 'watermarked');
      const position = options.position || 'bottom-right';
      
      const positionMap = {
        'top-left': '10:10',
        'top-right': 'W-w-10:10',
        'bottom-left': '10:H-h-10',
        'bottom-right': 'W-w-10:H-h-10',
        'center': '(W-w)/2:(H-h)/2'
      };
      
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .input(watermarkPath)
          .complexFilter([
            `[1:v]scale=100:100[watermark]`,
            `[0:v][watermark]overlay=${positionMap[position]}[output]`
          ])
          .map('[output]')
          .output(outputPath)
          .on('end', () => {
            console.log(chalk.green(`✅ Watermark added: ${outputPath}`));
            resolve(outputPath);
          })
          .on('error', reject)
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Watermark addition failed:'), error);
      throw error;
    }
  }

  async getVideoDuration(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          resolve(metadata.format.duration);
        }
      });
    });
  }

  async getVideoInfo(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          const { format, streams } = metadata;
          const videoStream = streams.find(s => s.codec_type === 'video');
          
          resolve({
            duration: format.duration,
            size: format.size,
            bitrate: format.bit_rate,
            width: videoStream?.width,
            height: videoStream?.height,
            fps: eval(videoStream?.r_frame_rate) || 30
          });
        }
      });
    });
  }

  generateOutputPath(inputPath, suffix) {
    const ext = path.extname(inputPath);
    const basename = path.basename(inputPath, ext);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return path.join(this.outputDir, `${basename}-${suffix}-${timestamp}${ext}`);
  }

  async cleanup() {
    try {
      // Clean up temp files
      const tempFiles = await fs.readdir(this.tempDir);
      for (const file of tempFiles) {
        await fs.remove(path.join(this.tempDir, file));
      }
      console.log(chalk.green('✅ Temp files cleaned up'));
    } catch (error) {
      console.error(chalk.red('❌ Cleanup failed:'), error);
    }
  }

  async processVideoForPlatform(inputPath, platform = 'youtube', options = {}) {
    try {
      console.log(chalk.blue(`🎯 Processing video for ${platform}`));
      
      // Convert to shorts format
      const shortsResult = await this.convertToShorts(inputPath, { platform });
      
      // Add captions if provided
      let finalPath = shortsResult.outputPath;
      if (options.caption) {
        finalPath = await this.addCaptions(finalPath, options.caption);
      }
      
      // Add watermark if provided
      if (options.watermark) {
        finalPath = await this.addWatermark(finalPath, options.watermark);
      }
      
      return {
        success: true,
        outputPath: finalPath,
        platform,
        metadata: await this.getVideoInfo(finalPath)
      };
    } catch (error) {
      console.error(chalk.red(`❌ Failed to process video for ${platform}:`), error);
      throw error;
    }
  }
}

export default VideoProcessor;
