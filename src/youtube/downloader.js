import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import config from '../config/config.js';

const execAsync = promisify(exec);

export class YouTubeDownloader {
  constructor() {
    this.ytDlpCommand = 'yt-dlp';
    this.downloadDir = config.downloads.outputDir.youtube;
  }

  async initialize() {
    try {
      // Ensure download directory exists
      await fs.ensureDir(this.downloadDir);

      // Check if yt-dlp is available
      try {
        await this.checkYtDlpAvailable();
        console.log(chalk.green('✅ yt-dlp found and working'));
      } catch (error) {
        console.log(chalk.yellow('⚠️ yt-dlp not found, trying to install...'));
        await this.installYtDlp();
      }

      console.log(chalk.green('✅ YouTube downloader initialized'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize YouTube downloader:'), error);
      throw error;
    }
  }

  async checkYtDlpAvailable() {
    try {
      const { stdout } = await execAsync(`${this.ytDlpCommand} --version`);
      console.log(chalk.blue(`yt-dlp version: ${stdout.trim()}`));
      return true;
    } catch (error) {
      throw new Error('yt-dlp not found');
    }
  }

  async installYtDlp() {
    try {
      console.log(chalk.blue('📥 Installing yt-dlp via pip...'));

      // Try to install via pip
      try {
        await execAsync('pip install yt-dlp');
        console.log(chalk.green('✅ yt-dlp installed via pip'));
        return;
      } catch (pipError) {
        console.log(chalk.yellow('⚠️ pip install failed, trying pip3...'));
      }

      // Try pip3
      try {
        await execAsync('pip3 install yt-dlp');
        console.log(chalk.green('✅ yt-dlp installed via pip3'));
        return;
      } catch (pip3Error) {
        console.log(chalk.yellow('⚠️ pip3 install failed, trying alternative method...'));
      }

      // Alternative: download binary directly
      await this.downloadYtDlpBinary();

    } catch (error) {
      console.error(chalk.red('❌ Failed to install yt-dlp:'), error);
      throw new Error('yt-dlp is required but could not be installed. Please install it manually: pip install yt-dlp');
    }
  }

  async downloadYtDlpBinary() {
    try {
      const binaryPath = './bin/yt-dlp';
      await fs.ensureDir('./bin');

      console.log(chalk.blue('📥 Downloading yt-dlp binary...'));

      // Download for Linux/Unix
      const downloadUrl = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp';
      const { exec } = await import('child_process');

      await new Promise((resolve, reject) => {
        exec(`curl -L "${downloadUrl}" -o "${binaryPath}"`, (error, stdout, stderr) => {
          if (error) {
            reject(error);
          } else {
            resolve(stdout);
          }
        });
      });

      // Make executable
      await execAsync(`chmod +x ${binaryPath}`);

      // Update command to use local binary
      this.ytDlpCommand = binaryPath;

      console.log(chalk.green('✅ yt-dlp binary downloaded successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to download yt-dlp binary:'), error);
      throw error;
    }
  }

  async getChannelVideos(channelUrl, maxVideos = 10) {
    try {
      console.log(chalk.blue(`🔍 Fetching videos from: ${channelUrl}`));

      // Use a simpler approach - get video URLs first, then get info
      const command = `${this.ytDlpCommand} "${channelUrl}" --flat-playlist --get-url --playlist-end ${maxVideos}`;
      const { stdout } = await execAsync(command);

      const videoUrls = stdout.split('\n').filter(line => line.trim() && line.includes('youtube.com/watch'));

      if (videoUrls.length === 0) {
        console.log(chalk.yellow(`⚠️ No videos found for ${channelUrl}`));
        return [];
      }

      // Get info for each video
      const videos = [];
      for (const url of videoUrls.slice(0, maxVideos)) {
        try {
          const info = await this.getVideoInfo(url);
          if (info) {
            videos.push({
              id: this.extractVideoId(url),
              title: info.title,
              duration: info.duration,
              uploadDate: 'Unknown',
              url: url
            });
          }
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Could not get info for ${url}`));
        }
      }

      console.log(chalk.green(`✅ Found ${videos.length} videos`));
      return videos;
    } catch (error) {
      console.error(chalk.red('❌ Failed to fetch channel videos:'), error);
      throw error;
    }
  }

  extractVideoId(url) {
    const match = url.match(/[?&]v=([^&]+)/);
    return match ? match[1] : 'unknown';
  }

  parseVideoList(output) {
    const lines = output.split('\n').filter(line => line.trim());
    return lines.map(line => {
      const parts = line.split('\t');
      return {
        id: parts[0],
        title: parts[1],
        duration: parts[2],
        uploadDate: parts[3],
        url: `https://www.youtube.com/watch?v=${parts[0]}`
      };
    }).filter(video => video.id && video.title);
  }

  async downloadVideo(videoUrl, options = {}) {
    try {
      console.log(chalk.blue(`⬇️ Downloading: ${videoUrl}`));

      const outputTemplate = path.join(
        this.downloadDir,
        '%(title)s-%(id)s.%(ext)s'
      );

      const format = options.quality === 'high' ? 'best[height<=720]' :
                     options.quality === 'medium' ? 'best[height<=480]' :
                     options.quality === 'low' ? 'worst' : 'best[height<=1080]';

      const command = `${this.ytDlpCommand} "${videoUrl}" --format "${format}" --output "${outputTemplate}" --write-info-json --write-thumbnail`;

      const { stdout } = await execAsync(command);

      // Get video ID to find the downloaded file
      const videoId = this.extractVideoId(videoUrl);
      const downloadedFile = await this.findDownloadedFile(videoId);

      console.log(chalk.green(`✅ Downloaded: ${downloadedFile}`));

      return {
        success: true,
        filePath: downloadedFile,
        metadata: await this.getVideoMetadata(downloadedFile)
      };
    } catch (error) {
      console.error(chalk.red(`❌ Failed to download: ${videoUrl}`), error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async findDownloadedFile(videoId) {
    try {
      const files = await fs.readdir(this.downloadDir);
      const videoFile = files.find(file =>
        file.includes(videoId) &&
        (file.endsWith('.mp4') || file.endsWith('.mkv') || file.endsWith('.webm'))
      );

      return videoFile ? path.join(this.downloadDir, videoFile) : null;
    } catch (error) {
      console.error(chalk.red('❌ Failed to find downloaded file:'), error);
      return null;
    }
  }



  extractFilePath(output) {
    const lines = output.split('\n');
    for (const line of lines) {
      if (line.includes('Destination:') || line.includes('[download]')) {
        const match = line.match(/(?:Destination:|to)\s+(.+)/);
        if (match) {
          return match[1].trim();
        }
      }
    }
    return null;
  }

  async getVideoMetadata(filePath) {
    try {
      const infoFile = filePath.replace(/\.[^.]+$/, '.info.json');
      if (await fs.pathExists(infoFile)) {
        const metadata = await fs.readJson(infoFile);
        return {
          title: metadata.title,
          description: metadata.description,
          duration: metadata.duration,
          uploader: metadata.uploader,
          uploadDate: metadata.upload_date,
          viewCount: metadata.view_count,
          tags: metadata.tags || []
        };
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️ Could not read metadata:'), error.message);
    }
    return null;
  }

  async downloadFromTrustedChannels(maxPerChannel = 2) {
    const results = [];

    for (const channel of config.youtube.trustedChannels) {
      try {
        console.log(chalk.blue(`📺 Processing channel: ${channel}`));

        // Construct proper channel URL for yt-dlp
        let channelUrl;
        if (channel.startsWith('@')) {
          channelUrl = `https://www.youtube.com/${channel}`;
        } else if (channel.startsWith('UC')) {
          channelUrl = `https://www.youtube.com/channel/${channel}`;
        } else if (channel.includes('youtube.com')) {
          channelUrl = channel;
        } else {
          channelUrl = `https://www.youtube.com/c/${channel}`;
        }

        const videos = await this.getChannelVideos(channelUrl, maxPerChannel);

        for (const video of videos.slice(0, maxPerChannel)) {
          const result = await this.downloadVideo(video.url);
          results.push({
            channel,
            video: video.title,
            ...result
          });

          // Rate limiting
          await new Promise(resolve => setTimeout(resolve, config.delays.action));
        }
      } catch (error) {
        console.error(chalk.red(`❌ Failed to process channel ${channel}:`), error);
        results.push({
          channel,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  async getVideoInfo(videoUrl) {
    try {
      const command = `${this.ytDlpCommand} "${videoUrl}" --print "%(title)s,%(duration)s,%(description)s,%(uploader)s" --no-download`;
      const { stdout } = await execAsync(command);

      const lines = stdout.split('\n').filter(line => line.trim());
      if (lines.length > 0) {
        const parts = lines[0].split(',');
        return {
          title: parts[0] || 'Unknown',
          duration: parts[1] || 'Unknown',
          description: parts[2] || 'No description',
          uploader: parts[3] || 'Unknown'
        };
      }
      return null;
    } catch (error) {
      console.error(chalk.red('❌ Failed to get video info:'), error);
      return null;
    }
  }

  async cleanup() {
    try {
      // Clean up old files (older than 7 days)
      const files = await fs.readdir(this.downloadDir);
      const now = Date.now();
      const weekAgo = now - (7 * 24 * 60 * 60 * 1000);
      
      for (const file of files) {
        const filePath = path.join(this.downloadDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime.getTime() < weekAgo) {
          await fs.remove(filePath);
          console.log(chalk.yellow(`🗑️ Cleaned up old file: ${file}`));
        }
      }
    } catch (error) {
      console.error(chalk.red('❌ Cleanup failed:'), error);
    }
  }
}

export default YouTubeDownloader;
